import type { Metadata } from "next";
import "./globals.css";
import { Toaster } from "@/components/ui/sonner";

export const metadata: Metadata = {
  title: "Marín Jaramillo - Ingeniería y Consultoría en Seguridad Industrial",
  description:
    "Especialistas en gestión y prevención del riesgo laboral con tecnología de vanguardia. Capacitación VR en Seguridad Industrial, evaluación de riesgos, inspecciones y auditorías.",
  keywords:
    "seguridad industrial, capacitación VR, realidad virtual, prevención riesgos, LOTO, espacios confinados, trabajos en altura",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        {children}
        <Toaster />
      </body>
    </html>
  );
}
