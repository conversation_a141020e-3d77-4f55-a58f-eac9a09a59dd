"use client";

import Image from "next/image";

const clients = [
  {
    name: "PROS<PERSON><PERSON>",
    logo: "/images/clients/proshale-logo.png",
    alt: "PROSHALE Logo",
  },
  {
    name: "La Campana",
    logo: "/images/clients/la-campana-logo.png",
    alt: "La Campana Logo",
  },
  {
    name: "PEPSICO",
    logo: "/images/clients/pepsico-logo.png",
    alt: "PepsiCo Logo",
  },
  {
    name: "CONFIPETROL",
    logo: "/images/clients/confipetrol-logo.png",
    alt: "Confipetrol Logo",
  },
  {
    name: "Urbase<PERSON>",
    logo: "/images/clients/urbaser-logo.png",
    alt: "Urbaser Logo",
  },
  {
    name: "Peldar",
    logo: "/images/clients/peldar-logo.png",
    alt: "Peldar Logo",
  },
  {
    name: "BANCOLDEX",
    logo: "/images/clients/bancoldex-logo.png",
    alt: "Bancoldex Logo",
  },
  {
    name: "EQUANS",
    logo: "/images/clients/equans-logo.png",
    alt: "Equans Logo",
  },
  {
    name: "Atlantica",
    logo: "/images/clients/atlantica-logo.png",
    alt: "Atlantica Sustainable Infrastructure Logo",
  },
  {
    name: "PATPRIMO",
    logo: "/images/clients/patprimo-logo.png",
    alt: "Patprimo Logo",
  },
  {
    name: "HOLSAN",
    logo: "/images/clients/holsan-logo.png",
    alt: "Holsan Logo",
  },
  {
    name: "Enercom",
    logo: "/images/clients/enercom-logo.png",
    alt: "Enercom Logo",
  },
];

export function ClientsSection() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
            Nuestros Clientes
          </h2>
          <div className="w-24 h-1 bg-red-600 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Empresas líderes confían en nuestra experiencia y servicios
            especializados
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-8 items-center">
          {clients.map((client) => (
            <div
              key={client.name}
              className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm"
            >
              <Image
                src={client.logo}
                alt={client.alt}
                width={120}
                height={60}
                className="max-w-full h-auto object-contain"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "/placeholder-logo.png";
                }}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
