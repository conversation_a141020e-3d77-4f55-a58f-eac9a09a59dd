import Image from "next/image";

export function Footer() {
  return (
    <footer className="bg-slate-800 text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-3 gap-8">
          <div>
            <div className="flex items-center space-x-4 mb-4">
              <Image
                src="/images/logo-white.png"
                alt="Marín Jaramillo Logo"
                width={40}
                height={40}
                className="rounded-full"
              />
              <div>
                <h3 className="text-lg font-bold"><PERSON><PERSON>mill<PERSON></h3>
                <p className="text-sm text-slate-300">
                  Ingeniería y Consultoría
                </p>
              </div>
            </div>
            <p className="text-slate-300">
              Especialistas en gestión y prevención del riesgo laboral con
              tecnología de vanguardia.
            </p>
          </div>
          <div>
            <h4 className="text-lg font-semibold mb-4">Servicios</h4>
            <ul className="space-y-2 text-slate-300">
              <li>Prevención del Riesgo Mecánico</li>
              <li>Evaluación de Riesgo en Máquinas</li>
              <li>Inspecciones de Seguridad</li>
              <li>Capacitación VR</li>
              <li>Auditorías</li>
            </ul>
          </div>
          <div>
            <h4 className="text-lg font-semibold mb-4">Contacto</h4>
            <div className="space-y-2 text-slate-300">
              <p><EMAIL></p>
              <p>+57 317 820 0390</p>
              <p>Colombia</p>
            </div>
          </div>
        </div>
        <div className="border-t border-slate-700 mt-8 pt-8 text-center text-slate-300">
          <p>
            &copy; {new Date().getFullYear()} Marín Jaramillo Ingeniería y
            Consultoría. Todos los derechos reservados.
          </p>
        </div>
      </div>
    </footer>
  );
}
