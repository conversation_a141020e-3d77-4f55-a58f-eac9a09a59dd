"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { Menu, X } from "lucide-react";

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  return (
    <header className="bg-slate-800 text-white sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-20">
          <div className="flex items-center space-x-6">
            <Image
              src="/images/logo-white.png"
              alt="Marín Jaramillo Logo"
              width={70}
              height={70}
              className="rounded-full shadow-lg"
            />
            <div>
              <h1 className="text-2xl font-bold">Marín <PERSON>mill<PERSON></h1>
              <p className="text-base text-slate-300">
                Ingeniería y Consultoría
              </p>
            </div>
          </div>

          <nav className="hidden md:flex space-x-8">
            <Link
              href="#inicio"
              className="hover:text-slate-300 transition-colors"
            >
              Inicio
            </Link>
            <Link
              href="#nosotros"
              className="hover:text-slate-300 transition-colors"
            >
              Nosotros
            </Link>
            <Link
              href="#servicios"
              className="hover:text-slate-300 transition-colors"
            >
              Servicios
            </Link>
            <Link href="#vr" className="hover:text-slate-300 transition-colors">
              Capacitación VR
            </Link>
            <Link
              href="#contacto"
              className="hover:text-slate-300 transition-colors"
            >
              Contacto
            </Link>
          </nav>

          <button
            type="button"
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {isMenuOpen && (
          <nav className="md:hidden py-4 border-t border-slate-700">
            <div className="flex flex-col space-y-4">
              <Link
                href="#inicio"
                className="hover:text-slate-300 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Inicio
              </Link>
              <Link
                href="#nosotros"
                className="hover:text-slate-300 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Nosotros
              </Link>
              <Link
                href="#servicios"
                className="hover:text-slate-300 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Servicios
              </Link>
              <Link
                href="#vr"
                className="hover:text-slate-300 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Capacitación VR
              </Link>
              <Link
                href="#contacto"
                className="hover:text-slate-300 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Contacto
              </Link>
            </div>
          </nav>
        )}
      </div>
    </header>
  );
}
