import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

export function HeroSection() {
  return (
    <section
      id="inicio"
      className="bg-linear-to-br from-slate-800 to-slate-900 text-white py-20"
    >
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Ingeniería y Consultoría en Seguridad Industrial
            </h1>
            <p className="text-xl mb-8 text-slate-300">
              Especialistas en gestión y prevención del riesgo laboral con
              tecnología de vanguardia y profesionales idóneos.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="#servicios">
                <Button size="lg" className="bg-red-600 hover:bg-red-700">
                  Conoce nuestros servicios
                </Button>
              </Link>
              <Link href="#vr">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-slate-800 bg-transparent"
                >
                  Capacitación VR
                </Button>
              </Link>
            </div>
          </div>
          <div className="flex justify-center">
            <Image
              src="/images/logo-white.png"
              alt="Marín Jaramillo Logo"
              width={400}
              height={400}
              priority
              className="rounded-full opacity-90"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
