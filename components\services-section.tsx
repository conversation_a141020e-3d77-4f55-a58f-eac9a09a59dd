"use client";

import { useState } from "react";
import Image from "next/image";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Shield,
  Cog,
  Search,
  Settings,
  Flame,
  FileText,
  Lock,
  Zap,
  CheckCircle,
} from "lucide-react";

const services = [
  {
    id: "machine-risk-evaluation",
    icon: Search,
    title: "Evaluación de Riesgo en Máquinas",
    description:
      "El proceso de evaluación de riesgos se realiza mediante la determinación de los límites de la máquina, identificación de peligros, estimación de Riesgos y evaluación del Riesgos. Analizamos las mejores medidas técnicas para aplicar en la maquinaria y equipo y emitimos recomendaciones de intervención acorde al cumplimiento normativo. Realizamos nuestra intervención basados en normas internacionales tales como ISO 12100 e ISO/TR14121-2.",
    image: "/images/industrial-services-overview.png",
  },
  {
    id: "machine-safety-inspections",
    icon: Cog,
    title: "Inspecciones de Seguridad en Máquinas y Equipos",
    description:
      "Verifique el estado de seguridad de sus máquinas y equipos mediante nuestras inspecciones técnicas de y diagnóstico de condiciones de riesgo en la operación. Las inspecciones de seguridad en maquinaria y equipo le permitirá verificar que las funciones de seguridad y protecciones de máquinas ese orden con el nivel de riesgo, protegiendo así la seguridad de sus trabajadores.",
    image: "/images/team-industrial-safety.png",
  },
  {
    id: "occupational-health-safety",
    icon: Shield,
    title: "Seguridad y Salud en el Trabajo",
    description:
      "Asesoría técnica para el diseño, administración y ejecución del SG SST. Implementación, mantenimiento y mejora del sistema de gestión de seguridad y salud en el trabajo.",
    image: "/images/industrial-vr-safety-worker.png",
  },
  {
    id: "industrial-safety",
    icon: Settings,
    title: "Seguridad Industrial",
    description:
      "Servicios especializados en seguridad industrial para la prevención de riesgos laborales y protección de trabajadores.",
    image: "/images/vr-industrial-safety-training.png",
  },
  {
    id: "industrial-hygiene",
    icon: Flame,
    title: "Higiene Industrial",
    description:
      "Evaluación y control de factores ambientales que pueden afectar la salud de los trabajadores en el ambiente laboral.",
    image: "/images/vr-training-benefits.png",
  },
  {
    id: "technical-investigation",
    icon: FileText,
    title: "Investigación en Área Técnica e Investigación de Accidentes",
    description:
      "Análisis técnico especializado para la investigación de incidentes y accidentes laborales, identificando causas y medidas preventivas.",
    image: "/images/contact-consultation.png",
  },
  {
    id: "technical-training",
    icon: FileText,
    title: "Programas de Capacitación Técnica",
    description:
      "Desarrollo e implementación de programas de formación técnica especializada en seguridad y salud ocupacional.",
    image: "/images/vr-training-classroom-session.png",
  },
  {
    id: "psychology-safety",
    icon: Shield,
    title: "Psicología en Seguridad y Salud en el Trabajo",
    description:
      "Servicios especializados en el factor humano aplicado a la seguridad y salud ocupacional, incluyendo evaluaciones psicológicas y programas de bienestar laboral.",
    image: "/images/vr-implementation-process.png",
  },
  {
    id: "lockout-tagout",
    icon: Lock,
    title: "Bloqueo y Etiquetado",
    description:
      "Realizamos un análisis especializado para determinar Procedimientos de Control de Energía Peligrosas, específicos para cada una de las máquinas y equipos de la empresa, basados en la norma OSHA 1910.147.",
    image: "/images/vr-solutions-scenarios.png",
  },
  {
    id: "hot-work",
    icon: Zap,
    title: "Trabajos en Caliente",
    description:
      "Análisis de las operaciones de la empresa, con el fin de determinar las mejores prácticas para la realización de trabajos en caliente. Realizamos inventario de tareas en caliente, documentación del programa, asesoría técnica, implementación de manual de permisos de trabajo, procedimientos técnicos y entrenamiento.",
    image: "/images/vr-headset-front-view.png",
  },
  {
    id: "audits",
    icon: CheckCircle,
    title: "Auditorías",
    description:
      "Auditores líderes e internos certificados evaluará la conformidad de los sistemas de gestión de su organización, proporcionando a la organización información clara y veraz respecto a su estado de cumplimiento. Profesionales especializados en cada uno de los sistemas de gestión, lo asesorarán a través de los requisitos de las normas y la forma de implementarlos de acuerdo a las características de sus organización.",
    image: "/images/vr-headset-side-view.png",
  },
];

export function ServicesSection() {
  const [selectedService, setSelectedService] = useState<string | null>(null);
  const [hoveredService, setHoveredService] = useState<string | null>(null);

  return (
    <section id="servicios" className="py-20">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
            Nuestros Servicios
          </h2>
          <div className="w-24 h-1 bg-red-600 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Ofrecemos servicios especializados en ingeniería de seguridad
            industrial y gestión de riesgos laborales
          </p>

          {/* Dynamic Image Display */}
          <div className="flex justify-center mb-12">
            <div className="relative">
              <Image
                src={
                  hoveredService
                    ? services.find((s) => s.id === hoveredService)?.image ||
                      "/images/industrial-services-overview.png"
                    : selectedService
                    ? services.find((s) => s.id === selectedService)?.image ||
                      "/images/industrial-services-overview.png"
                    : "/images/industrial-services-overview.png"
                }
                alt="Industrial safety services overview"
                width={800}
                height={400}
                className="rounded-lg shadow-xl w-full max-w-4xl object-cover transition-all duration-500 ease-in-out"
              />
              {(hoveredService || selectedService) && (
                <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white px-4 py-2 rounded-lg">
                  <p className="text-sm font-medium">
                    {
                      services.find(
                        (s) => s.id === (hoveredService || selectedService),
                      )?.title
                    }
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service) => (
            <Card
              key={service.id}
              className="cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-105 hover:border-red-500"
              onMouseEnter={() => setHoveredService(service.id)}
              onMouseLeave={() => setHoveredService(null)}
              onClick={() =>
                setSelectedService(
                  selectedService === service.id ? null : service.id,
                )
              }
            >
              <CardHeader>
                <service.icon className="w-12 h-12 text-red-600 mb-4 transition-transform duration-300 hover:scale-110" />
                <CardTitle className="text-slate-800 hover:text-red-600 transition-colors duration-300">
                  {service.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-700">
                  {service.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
