import Image from "next/image";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Shield,
  Cog,
  Search,
  Settings,
  Flame,
  FileText,
  Lock,
  Zap,
  CheckCircle,
} from "lucide-react";

const services = [
  {
    icon: Search,
    title: "Evaluación de Riesgo en Máquinas",
    description:
      "El proceso de evaluación de riesgos se realiza mediante la determinación de los límites de la máquina, identificación de peligros, estimación de Riesgos y evaluación del Riesgos. Analizamos las mejores medidas técnicas para aplicar en la maquinaria y equipo y emitimos recomendaciones de intervención acorde al cumplimiento normativo. Realizamos nuestra intervención basados en normas internacionales tales como ISO 12100 e ISO/TR14121-2.",
  },
  {
    icon: Cog,
    title: "Inspecciones de Seguridad en Máquinas y Equipos",
    description:
      "Verifique el estado de seguridad de sus máquinas y equipos mediante nuestras inspecciones técnicas de y diagnóstico de condiciones de riesgo en la operación. Las inspecciones de seguridad en maquinaria y equipo le permitirá verificar que las funciones de seguridad y protecciones de máquinas ese orden con el nivel de riesgo, protegiendo así la seguridad de sus trabajadores.",
  },
  {
    icon: Shield,
    title: "Seguridad y Salud en el Trabajo",
    description:
      "Asesoría técnica para el diseño, administración y ejecución del SG SST. Implementación, mantenimiento y mejora del sistema de gestión de seguridad y salud en el trabajo.",
  },
  {
    icon: Settings,
    title: "Seguridad Industrial",
    description:
      "Servicios especializados en seguridad industrial para la prevención de riesgos laborales y protección de trabajadores.",
  },
  {
    icon: Flame,
    title: "Higiene Industrial",
    description:
      "Evaluación y control de factores ambientales que pueden afectar la salud de los trabajadores en el ambiente laboral.",
  },
  {
    icon: FileText,
    title: "Investigación en Área Técnica e Investigación de Accidentes",
    description:
      "Análisis técnico especializado para la investigación de incidentes y accidentes laborales, identificando causas y medidas preventivas.",
  },
  {
    icon: FileText,
    title: "Programas de Capacitación Técnica",
    description:
      "Desarrollo e implementación de programas de formación técnica especializada en seguridad y salud ocupacional.",
  },
  {
    icon: Shield,
    title: "Psicología en Seguridad y Salud en el Trabajo",
    description:
      "Servicios especializados en el factor humano aplicado a la seguridad y salud ocupacional, incluyendo evaluaciones psicológicas y programas de bienestar laboral.",
  },
  {
    icon: Lock,
    title: "Bloqueo y Etiquetado",
    description:
      "Realizamos un análisis especializado para determinar Procedimientos de Control de Energía Peligrosas, específicos para cada una de las máquinas y equipos de la empresa, basados en la norma OSHA 1910.147.",
  },
  {
    icon: Zap,
    title: "Trabajos en Caliente",
    description:
      "Análisis de las operaciones de la empresa, con el fin de determinar las mejores prácticas para la realización de trabajos en caliente. Realizamos inventario de tareas en caliente, documentación del programa, asesoría técnica, implementación de manual de permisos de trabajo, procedimientos técnicos y entrenamiento.",
  },
  {
    icon: CheckCircle,
    title: "Auditorías",
    description:
      "Auditores líderes e internos certificados evaluará la conformidad de los sistemas de gestión de su organización, proporcionando a la organización información clara y veraz respecto a su estado de cumplimiento. Profesionales especializados en cada uno de los sistemas de gestión, lo asesorarán a través de los requisitos de las normas y la forma de implementarlos de acuerdo a las características de sus organización.",
  },
];

export function ServicesSection() {
  return (
    <section id="servicios" className="py-20">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
            Nuestros Servicios
          </h2>
          <div className="w-24 h-1 bg-red-600 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Ofrecemos servicios especializados en ingeniería de seguridad
            industrial y gestión de riesgos laborales
          </p>
          <div className="flex justify-center mb-12">
            <Image
              src="/images/industrial-services-overview.png"
              alt="Industrial safety services overview: machine inspection, risk assessment, safety equipment, and workplace evaluation"
              width={600}
              height={300}
              className="rounded-lg shadow-lg"
            />
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card key={index}>
              <CardHeader>
                <service.icon className="w-12 h-12 text-red-600 mb-4" />
                <CardTitle className="text-slate-800">
                  {service.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-700">
                  {service.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
