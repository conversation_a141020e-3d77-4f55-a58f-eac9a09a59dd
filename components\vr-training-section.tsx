import Image from "next/image";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import {
  Eye,
  Shield,
  TrendingUp,
  Clock,
  DollarSign,
  CheckCircle,
  Settings,
  BarChart3,
  Users,
  Lock,
  AlertTriangle,
  Zap,
  Mountain,
  Building,
} from "lucide-react";

const vrBenefits = [
  {
    icon: Eye,
    title: "Aprendizaje Experiencial",
    description: "Entrenamiento práctico en entornos simulados y seguros.",
  },
  {
    icon: Shield,
    title: "Simulaciones Realistas",
    description:
      "Escenarios de riesgo como interacción con máquinas, LOTO, energía eléctrica, incendios, caídas y espacios confinados.",
  },
  {
    icon: TrendingUp,
    title: "Mayor Retención",
    description:
      "75% mejor retención de información que los métodos tradicionales.",
  },
  {
    icon: Clock,
    title: "Ahorro de Tiempo",
    description: "Reducción del 40% en el tiempo total de capacitación.",
  },
  {
    icon: DollarSign,
    title: "Reducción de Costos",
    description: "Disminución del 30% en costos asociados a la capacitación.",
  },
  {
    icon: CheckCircle,
    title: "Mejora Continua",
    description: "Evaluación objetiva del desempeño individual.",
  },
];

const vrSolutions = [
  {
    icon: Lock,
    title: "LOTO",
    description:
      "Procedimientos de bloqueo y etiquetado de energía peligrosa para mantenimiento seguro.",
    image: "/images/loto.png",
  },
  {
    icon: AlertTriangle,
    title: "Condiciones Inseguras",
    description:
      "Identificación y corrección de riesgos en el ambiente laboral.",
    image: "/images/unsecure-conditions.png",
  },
  {
    icon: Zap,
    title: "Mantenimiento Eléctrico",
    description:
      "Capacitación en seguridad eléctrica y procedimientos de trabajo en sistemas energizados.",
    image: "/images/electric-maintainance.png",
  },
  {
    icon: Mountain,
    title: "Trabajos en Altura",
    description:
      "Entrenamiento en uso de equipos de protección y técnicas de trabajo seguro en alturas.",
    image: "/images/height-jobs.png",
  },
  {
    icon: Building,
    title: "Espacio Confinado",
    description:
      "Procedimientos de entrada segura y rescate en espacios confinados.",
    image: "/images/confinated-spaces.png",
  },
];

const platformFeatures = [
  {
    icon: Settings,
    title: "Escenarios Personalizables",
    description:
      "Amplia biblioteca de simulaciones adaptables a sus necesidades específicas.",
  },
  {
    icon: BarChart3,
    title: "Seguimiento del Progreso",
    description:
      "Monitoreo individual y grupal para una mejora constante del desempeño.",
  },
  {
    icon: Users,
    title: "Gestión SST más Eficiente",
    description:
      "Soluciones que faciliten el trabajo de la operación de forma más efectiva con mayor impacto en los trabajadores.",
  },
];

const whyChooseUsReasons = [
  "Capacitación inmersiva sin riesgos físicos",
  "Mejor retención del conocimiento y mayor aplicabilidad en el entorno real",
  "Fácil implementación",
  "Evaluar desempeño en un entorno simulado",
  "Entrenamiento indoor sin necesidad de requerimientos adicionales",
];

export function VRTrainingSection() {
  return (
    <section id="vr" className="py-20 bg-slate-800 text-white">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <div className="mb-16">
          <div className="grid md:grid-cols-2 gap-12 items-center mb-12">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Capacitación VR en Seguridad Industrial
              </h2>
              <div className="w-24 h-1 bg-red-600 mb-8"></div>
              <p className="text-xl text-slate-300">
                Transformando la seguridad industrial con tecnología inmersiva.
                Logre una reducción significativa de accidentes y mejore el
                rendimiento de su equipo con nuestra solución VR.
              </p>
            </div>
            <div className="flex justify-center">
              <Image
                src="/images/industrial-vr-safety-worker.png"
                alt="Workers using VR headsets for industrial safety training"
                width={600}
                height={480}
                className="rounded-lg shadow-xl w-full max-w-2xl object-cover"
              />
            </div>
          </div>
        </div>

        {/* Why VR Section */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold mb-4 text-white">
              ¿Por qué la Realidad Virtual en Seguridad Industrial?
            </h3>
            <div className="w-24 h-1 bg-red-600 mx-auto mb-8"></div>
          </div>
          <div className="grid lg:grid-cols-2 gap-12 items-center mb-12">
            <div className="flex justify-center">
              <Image
                src="/images/vr-industrial-safety-training.png"
                alt="Industrial workers wearing VR headsets for safety training in a warehouse environment"
                width={600}
                height={480}
                className="rounded-lg shadow-xl w-full max-w-2xl object-cover"
              />
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {vrBenefits.map((benefit) => (
                <Card
                  key={benefit.title}
                  className="bg-slate-700 border-slate-600 text-white"
                >
                  <CardHeader>
                    <benefit.icon className="w-12 h-12 text-red-400 mb-4" />
                    <CardTitle>{benefit.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-slate-300">
                      {benefit.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Platform Features Section */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold mb-4 text-white">
              Nuestra Plataforma de Capacitación VR
            </h3>
            <div className="w-24 h-1 bg-red-600 mx-auto"></div>
          </div>
          <div className="flex justify-center mb-12">
            <Image
              src="/images/vr-training-industrial-safety-banner.png"
              alt="Industrial safety VR training banner"
              width={700}
              height={400}
              className="rounded-lg shadow-xl w-full max-w-4xl object-cover"
            />
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {platformFeatures.map((feature) => (
              <Card
                key={feature.title}
                className="bg-slate-700 border-slate-600 text-white"
              >
                <CardHeader>
                  <feature.icon className="w-12 h-12 text-red-400 mb-4" />
                  <CardTitle>{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-slate-300">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Solutions Section */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold mb-4 text-white">
              Soluciones para cada caso
            </h3>
            <div className="w-24 h-1 bg-red-600 mx-auto"></div>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
            {vrSolutions.map((solution) => (
              <Card
                key={solution.title}
                className="group bg-slate-700/90 border-slate-600/50 text-white hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] hover:bg-slate-700 backdrop-blur-sm overflow-hidden"
              >
                <div className="relative overflow-hidden">
                  <Image
                    src={solution.image}
                    alt={`VR training scenario for ${solution.title}`}
                    width={400}
                    height={320}
                    className="w-full h-64 object-cover object-center transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-slate-900/70 via-transparent to-transparent" />
                  <div className="absolute top-4 left-4 bg-red-600 rounded-lg p-2.5">
                    <solution.icon className="w-5 h-5 text-white" />
                  </div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <h3 className="text-xl font-bold text-white mb-1 drop-shadow-lg">
                      {solution.title}
                    </h3>
                  </div>
                </div>
                <CardContent className="p-6">
                  <p className="text-slate-300 leading-relaxed text-sm">
                    {solution.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Why Choose Us Section */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold mb-4 text-white">
              ¿Por Qué Elegirnos?
            </h3>
            <div className="w-24 h-1 bg-red-600 mx-auto mb-8"></div>
            <p className="text-lg text-slate-300 max-w-3xl mx-auto mb-8">
              Mejorar la formación en HSE con Realidad Virtual: una experiencia
              segura, envolvente y eficiente.
            </p>
            <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto">
              {whyChooseUsReasons.map((reason) => (
                <div key={reason} className="text-left">
                  <CheckCircle className="w-6 h-6 text-green-400 inline mr-2" />
                  <span>{reason}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Call to Action Section */}
        <div className="text-center">
          <h3 className="text-3xl md:text-4xl font-bold mb-4 text-white">
            Tecnología aplicada a la gestión SST
          </h3>
          <div className="w-24 h-1 bg-red-600 mx-auto mb-8"></div>
          <p className="text-lg text-slate-300 max-w-3xl mx-auto">
            Adopte la tecnología VR y proteja a sus empleados. Contáctenos hoy
            para una demostración y un presupuesto personalizado.
          </p>
        </div>
      </div>
    </section>
  );
}
